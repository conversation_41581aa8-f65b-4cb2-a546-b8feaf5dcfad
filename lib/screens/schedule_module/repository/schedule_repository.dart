import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/screens/schedule_module/repository/schedule_provider.dart';

import '../model/schedule_date_detail_model.dart';
import '../model/schedule_month_model.dart';

class scheduleApiRepository {
  final scheduleProvider = scheduleApiProvider();

  Future<List<ScheduleMonthResponseModel>?> scheduleWeekApi(
      {required BuildContext context, required String iosYearWeek}) async {
    final response = await scheduleProvider.scheduleWeekApiCall(context, iosYearWeek);
    log('----------->week-list ${response?.data}');

    if (response != null && response.data is List<dynamic>) {
      // List<dynamic> weekList = response.data;
      List<dynamic> weekList = [
    {
        "PersonId": "73d38a09-e0be-44c8-9a1c-c3f96c621407",
        "CostDepartment": "FashionWel",
        "Date": "2025-08-05T00:00:00",
        "ISODate": "20250805",
        "TimeFrom": "12:00",
        "BreakTime": "00:30",
        "CostCenters": "Wel",
        "Service": "well 12/20"
    },
    {
        "PersonId": "73d38a09-e0be-44c8-9a1c-c3f96c621407",
        "CostDepartment": "FashionWel",
        "Date": "2025-08-06T00:00:00",
        "ISODate": "20250806",
        "TimeFrom": "12:00",
        "BreakTime": "00:30",
        "CostCenters": "Wel",
        "Service": "well 12/20"
    },
    {
        "PersonId": "73d38a09-e0be-44c8-9a1c-c3f96c621407",
        "CostDepartment": "FashionWel",
        "Date": "2025-08-07T00:00:00",
        "ISODate": "20250807",
        "TimeFrom": "12:00",
        "BreakTime": "00:30",
        "CostCenters": "Wel",
        "Service": "well 12/20"
    },
    {
        "PersonId": "73d38a09-e0be-44c8-9a1c-c3f96c621407",
        "CostDepartment": "FashionWel",
        "Date": "2025-08-08T00:00:00",
        "ISODate": "20250808",
        "TimeFrom": "10:00",
        "BreakTime": "00:30",
        "CostCenters": "Wel",
        "Service": "well 10/18"
    },
    {
        "PersonId": "73d38a09-e0be-44c8-9a1c-c3f96c621407",
        "CostDepartment": "FashionWel",
        "Date": "2025-08-10T00:00:00",
        "ISODate": "20250810",
        "TimeFrom": "12:00",
        "BreakTime": "00:30",
        "CostCenters": "Wel",
        "Service": "well 12/20"
    }
];
      print('----------->week-list ${weekList.length}');
      List<ScheduleMonthResponseModel> weekDetailList = [];
      weekDetailList.clear();

      for (var item in weekList) {
        weekDetailList.add(ScheduleMonthResponseModel.fromJson(item, response.statusCode!));
      }

      return weekDetailList;
    } else {
      return null;
    }
  }

  Future<List<ScheduleMonthResponseModel>?> scheduleMonthApi(
      {required BuildContext context, required String iosYearMonth}) async {
    final response = await scheduleProvider.scheduleMonthApiCall(context, iosYearMonth);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> monthList = response.data;
      List<ScheduleMonthResponseModel> monthDetailList = [];

      for (var item in monthList) {
        monthDetailList.add(ScheduleMonthResponseModel.fromJson(item, response.statusCode!));
      }

      return monthDetailList;
    } else {
      return null;
    }
  }

  Future<List<ScheduleDateDepartmentListResponseModel>?> scheduleDateDepartmentApi(
      {required BuildContext context, required String iSODate}) async {
    final response = await scheduleProvider.scheduleDateDepartmentApiCall(context, iSODate);
    log("api called ==========>${response}");
    if (response != null && response.data is List<dynamic>) {
      List<dynamic> departmentList = response.data;
      List<ScheduleDateDepartmentListResponseModel> departmentDetailList = [];

      for (var item in departmentList) {
        departmentDetailList.add(ScheduleDateDepartmentListResponseModel.fromJson(item, response.statusCode!));
      }

      return departmentDetailList;
    } else {
      return null;
    }
  }

  Future<List<ScheduleDateDetailResponseModel>?> scheduleDateApi(
      {required BuildContext context, required String iosDate, required String guid}) async {
    final response = await scheduleProvider.scheduleDateApiCall(context, iosDate, guid);

    if (response != null && response.data is List<dynamic>) {
      List<dynamic> dateList = response.data;
      List<ScheduleDateDetailResponseModel> dateDetailList = [];

      for (var item in dateList) {
        dateDetailList.add(ScheduleDateDetailResponseModel.fromJson(item, response.statusCode!));
      }
      log('dateDetailList ---------->$dateDetailList');
      return dateDetailList;
    } else {
      return null;
    }
  }

  Future<Response?> scheduleCalendarMonthApi({required BuildContext context, required String iosYearMonth}) async {
    final response = await scheduleProvider.scheduleCalendarMonthApiCall(context, iosYearMonth);

    return response;
  }

  Future<Response?> setSwapAndServiceApi(
      {required BuildContext context,
      required String personId,
      required String dateEntryId,
      required String stateId,
      required String nextStateId,
      required String remark}) async {
    final response = await scheduleProvider.setSwapAndServiceApiCall(
      context,
      personId,
      dateEntryId,
      stateId,
      nextStateId,
      remark,
    );

    return response;
  }

  Future<Response?> cancelSwapAndServiceApi({
    required BuildContext context,
    required String personId,
    required String dateEntryId,
    required String stateId,
    required String nextStateId,
  }) async {
    final response = await scheduleProvider.cancelSwapAndServiceApiCall(
      context,
      personId,
      dateEntryId,
      stateId,
      nextStateId,
    );

    return response;
  }
}
