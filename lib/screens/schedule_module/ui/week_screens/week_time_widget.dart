import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/week_screens/schedule_week_detail_screen.dart';

import '../../../../utils/app_navigation/appnavigation.dart';
import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';
import '../../bloc/schedule_cubit.dart';

class WeekDetailWidget extends StatelessWidget {
  final int index;
  const WeekDetailWidget({Key? key, required this.index}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final scheduleBloc = BlocProvider.of<ScheduleCubit>(context);

    // DateTime scheduleWeekTimeFrom =
    //     DateFormat('HH:mm').parse(scheduleBloc.scheduleWeekList1[index].timeFrom.toString());
    // // String timeWeekFromDate = DateFormat('h:mm a').format(scheduleWeekTimeFrom);

    // DateTime scheduleMonthTimeTo =
    //     DateFormat('HH:mm').parse(scheduleBloc.scheduleWeekList1[index].timeUntil.toString());
    // // String timeUntilDate = DateFormat('h:mm a').format(scheduleMonthTimeTo);

    return GestureDetector(
      onTap: () {
        AppNavigation.nextScreen(
          context,
          ScheduleWeekTimeDetailScreen(
            scheduleWeekData: scheduleBloc.scheduleWeekList1[index],
          ),
        );
      },
      child: Container(
        color: context.themeColors.homeContainerColor,
        margin: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h10),
        child: Padding(
          padding: EdgeInsets.only(left: AppSize.sp14),
          child: Column(
            children: [
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            '${scheduleBloc.scheduleWeekList1[index].timeFrom ?? ''} - ',
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.themeColors.textColor,
                              fontSize: AppSize.sp14,
                            ),
                          ),
                          Text(
                            '${scheduleBloc.scheduleWeekList1[index].timeUntil?? ''}',
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.themeColors.textColor,
                              fontSize: AppSize.sp14,
                            ),
                          ),
                        ],
                      ),
                      SpaceV(AppSize.h2),
                      Text(
                        scheduleBloc.scheduleWeekList1[index].department
                            .toString(),
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.themeColors.textColor,
                          fontSize: AppSize.sp12,
                        ),
                      ),
                    ],
                  ),
                  Spacer(),
                  (scheduleBloc.scheduleWeekList1[index].swap?.state ==
                          'Aangevraagd')
                      ? Icon(
                          Ionicons.swap_horizontal_outline,
                          size: AppSize.sp20,
                          color: AppColors.primaryColor,
                        )
                      : (scheduleBloc.scheduleWeekList1[index].openService !=
                              null)
                          ? Icon(
                              Ionicons.create_outline,
                              size: AppSize.sp20,
                              color: AppColors.primaryColor,
                            )
                          : Container(),
                  Padding(
                    padding:
                        EdgeInsets.only(right: AppSize.w10, left: AppSize.w4),
                    child: Icon(
                      Icons.arrow_forward_ios_sharp,
                      size: AppSize.sp14,
                      color: context.themeColors.iconColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
