import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/activity_screen/bloc/activity_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/widget/search_text_field.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class ActivitySearchListScreen extends StatelessWidget {
  final String selectedActivityName;
  final String selectedActivityId;
  final int? activityIndex;

  const ActivitySearchListScreen(
      {Key? key,
      this.activityIndex,
      required this.selectedActivityName,
      this.selectedActivityId = ''})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    context.read<ActivityCubit>().fetchData(
        selectedValue: selectedActivityName, selectedId: selectedActivityId);
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(
        actions: true,
        isLeading: true,
        leading: GestureDetector(
            onTap: () {
              AppNavigation.previousScreen(context);
            },
            child: Icon(Ionicons.close_outline)),
        title:
            '${AppLocalizations.of(context)!.activityText} ${activityIndex ?? 0 + 1}',
      ),
      body: BlocBuilder<ActivityCubit, ActivityState>(
        builder: (ctx, state) {
          final activityBloc = ctx.read<ActivityCubit>();

          return Column(
            children: [
              HourSearchTextField(
                onChanged: (value) {
                  activityBloc.filterActivity(value);
                },
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: activityBloc.filterActivityList.length,
                  itemBuilder: (context, index) {
                    final activityName = activityBloc.filterActivityList[index];
                    return GestureDetector(
                      onTap: () {
                        activityBloc.selectedActivityName = activityName.title;
                        activityBloc.selectedActivityId =
                            activityName.costCenterId;

                        final result = {
                          'selectedValue': activityBloc.selectedActivityName,
                          'selectedIndex': activityIndex,
                          'selectedActivityId': activityBloc.selectedActivityId
                        };
                        Navigator.pop(context, result);
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                            left: AppSize.w16, top: AppSize.h14),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: AppSize.w16,
                              height: AppSize.w16,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: activityBloc.selectedActivityName ==
                                          activityName.title
                                      ? AppColors.primaryColor
                                      : Colors.grey,
                                ),
                                color: activityBloc.selectedActivityName ==
                                        activityName.title
                                    ? AppColors.primaryColor
                                    : null,
                              ),
                              child: Center(
                                child: activityBloc.selectedActivityName ==
                                        activityName.title
                                    ? Icon(
                                        Icons.check,
                                        size: AppSize.sp14,
                                        color: Colors.white,
                                      )
                                    : null,
                              ),
                            ),
                            SpaceH(AppSize.w20),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    activityName.title,
                                    style:
                                        context.textTheme.bodyMedium?.copyWith(
                                      color: activityBloc
                                                  .filterActivityList[index]
                                                  .cssClass ==
                                              CssClass.A_N_PR
                                          ? Color(0xff0202ff)
                                          : context.themeColors.textColor,
                                      fontSize: AppSize.sp15,
                                    ),
                                  ),
                                  SpaceV(AppSize.h10),
                                  Container(
                                    color: context
                                        .themeColors.dividerAvailbilityColor,
                                    width: double.infinity,
                                    height: AppSize.h1,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
