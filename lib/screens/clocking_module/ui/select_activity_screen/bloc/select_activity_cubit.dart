import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activity_exclusions_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_exclusions_list.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/activity_list_repository.dart';

part 'select_activity_state.dart';

class SelectActivityCubit extends Cubit<SelectActivityState> {
  SelectActivityCubit() : super(SelectActivityInitial());

  List<ActivityListResponseModel> activityList = [];
  List<ActivityExclusionsListResponseModel> activityExclusionsList = [];
  List<ActivityListResponseModel> timeBondFalseList = [];
  String selectedActivityName = '';
  String selectedActivityId = '';
  List<ActivityListResponseModel> filterActivityList = [];
  ValueNotifier<bool> isLoading = ValueNotifier(false);

  // Cache for activity data by date to prevent repeated API calls
  Map<String, List<ActivityListResponseModel>> activityDataCache = {};
  String? lastFetchedDate;

  Future<void> setActivityData(
      {required String selectedValue,
      required String selectedId,
      required BuildContext context}) async {
    isLoading.value = true;
    selectedActivityName = selectedValue;
    selectedActivityId = selectedId;

    // Get current date for API call
    final currentDate = DateTime.now();
    final formattedDate =
        '${currentDate.year}${currentDate.month.toString().padLeft(2, '0')}${currentDate.day.toString().padLeft(2, '0')}';

    // Check if we need to fetch data
    if (activityList.isEmpty || lastFetchedDate != formattedDate) {
      // First fetch activity exclusions
      await activityExclusionsApiCall(context: context);

      // Then fetch activity data
      await costCentersActivityApiCall(
          context: context, selectedDate: formattedDate);

      lastFetchedDate = formattedDate;
    }

    // Set up filter list
    filterActivityList = List.from(activityList);
    isLoading.value = false;
    emit(SelectActivityInitial());
  }

  Future<void> costCentersActivityApiCall(
      {required BuildContext context, required String selectedDate}) async {
    // Check cache first
    if (activityDataCache.containsKey(selectedDate)) {
      final cachedData = activityDataCache[selectedDate]!;
      _processActivityData(cachedData);
      return;
    }

    final ActivityListApiRepository activityListApiRepository =
        ActivityListApiRepository();
    final response = await activityListApiRepository.activityListApi(
      context: context,
      selectedDate: selectedDate,
    );

    if (response != null && response.isNotEmpty) {
      // Cache the response data
      activityDataCache[selectedDate] = response;

      // Process the data
      _processActivityData(response);
    }
    emit(SelectActivityInitial());
  }

  // Helper method to process activity data
  void _processActivityData(List<ActivityListResponseModel> response) {
    timeBondFalseList.clear();
    activityList.clear();

    List<ActivityListResponseModel> unFilteredActivityListTemp = response;
    List<ActivityListResponseModel> unFilteredActivityList = [];

    // Filter out excluded activities
    for (var element in unFilteredActivityListTemp) {
      bool isExcluded = activityExclusionsList
          .any((exclusion) => exclusion.costCenterId == element.costCenterId);
      if (!isExcluded) {
        unFilteredActivityList.add(element);
      }
    }

    // Process activities based on their properties
    unFilteredActivityList.forEach((element) {
      if (element.timeBound == false && element.valid == true) {
        timeBondFalseList.add(element);
      }
      if (element.productive == true &&
          element.valid == true &&
          element.timeBound == true) {
        activityList.add(element);
      }
    });

    unFilteredActivityList.forEach((element) {
      if (element.productive == false &&
          element.valid == true &&
          element.timeBound == true) {
        activityList.add(element);
      }
    });
  }

  Future<void> activityExclusionsApiCall(
      {required BuildContext context}) async {
    final ActivityExclusionsApiRepository activityExclusionsApiRepository =
        ActivityExclusionsApiRepository();
    final response = await activityExclusionsApiRepository
        .activityExclusionsListApi(context: context);

    if (response != null && response.isNotEmpty) {
      activityExclusionsList.clear();
      activityExclusionsList.addAll(response);
    }
    emit(SelectActivityInitial());
  }

  void filterActivity(String query) {
    filterActivityList = activityList
        .where((element) =>
            element.title.toLowerCase().contains(query.toLowerCase()))
        .toList();
    emit(SelectActivityInitial());
  }

  // Method to clear cache when needed (e.g., on app restart or data refresh)
  void clearActivityCache() {
    activityDataCache.clear();
    lastFetchedDate = null;
  }

  // Method to get cached data for a specific date
  List<ActivityListResponseModel>? getCachedActivityData(String date) {
    return activityDataCache[date];
  }
}
