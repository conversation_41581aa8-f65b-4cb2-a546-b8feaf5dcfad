import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/availability_module/bloc/availability_cubit.dart';
import 'package:staff_medewerker/screens/availability_module/ui/week_availability_info_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../common/custom_widgets/shimmer_effect.dart';
import '../../../common/custom_widgets/spacebox.dart';
import '../../../utils/appsize.dart';
import '../../../utils/colors/app_colors.dart';

class AvailabilityScreen extends StatefulWidget {
  AvailabilityScreen({Key? key}) : super(key: key);

  @override
  State<AvailabilityScreen> createState() => _AvailabilityScreenState();
}

class _AvailabilityScreenState extends State<AvailabilityScreen> {
  void initState() {
    super.initState();

    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      availabilityBloc.fetchAllYearWeekAvailabilityData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final availabilityBloc = BlocProvider.of<AvailabilityCubit>(context);

    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.availabilityAppbarText,
      ),
      body: RefreshIndicator(
        color: AppColors.primaryColor,
        onRefresh: () async {
          availabilityBloc.fetchAllYearWeekAvailabilityData();
        },
        child: CustomScrollView(
          slivers: <Widget>[
            for (int i = availabilityBloc.currentYear;
                i <= availabilityBloc.currentYear + 1;
                i++) ...{
              SliverStickyHeader(
                header: Container(
                  height: AppSize.h40,
                  color: context.themeColors.listGridColor1,
                  padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '$i',
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp15,
                    ),
                  ),
                ),
                sliver: BlocBuilder<AvailabilityCubit, AvailabilityState>(
                  builder: (context, state) {
                    return ValueListenableBuilder(
                      valueListenable: availabilityBloc.isFirstAPICallCompleted,
                      builder: (context, value, child) {
                       return SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (BuildContext context, int index) {
                              int year = availabilityBloc.currentYear + i - 1;
                              availabilityBloc.remainingWeeks =
                                  53 - availabilityBloc.currentWeekNumber;
                              int weekNumber = (i ==
                                      availabilityBloc.currentYear)
                                  ? availabilityBloc.currentWeekNumber + index
                                  : index + 1;
                              DateTimeRange dateRange = availabilityBloc
                                  .getISOWeekDateRange(i, weekNumber);
                              // print('dateRange-----$dateRange');
                              // print('dateRange-----${dateRange}');
                              bool isDataAvailable = availabilityBloc
                                      .weekDaysEditedMap.value[weekNumber] ??
                                  false;
                              log("availabilityBloc.weekDaysEditedMap.value[weekNumber] ${availabilityBloc.availableMainWeekInfo?.EditRight}");

                              if (!availabilityBloc
                                  .isFirstAPICallCompleted.value) {
                                return Container(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.90,
                                            child: ShimmerWidget(
                                              margin: EdgeInsets.symmetric(
                                                  horizontal: AppSize.w10,
                                                  vertical: AppSize.h8),
                                              height: AppSize.h10,
                                            ),
                                          ),
                                          Expanded(
                                            child: IconButton(
                                              onPressed: () {},
                                              icon: Icon(
                                                Ionicons
                                                    .chevron_forward_outline,
                                                color: context
                                                    .themeColors.greyColor,
                                                size: AppSize.sp18,
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                      Padding(
                                        padding:
                                            EdgeInsets.only(left: AppSize.w12),
                                        child: Divider(
                                          thickness: 1,
                                          height: 0,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              } else {
                                 print(
                            '====>> ${availabilityBloc.availableMainWeekInfo?.iSOYearWeek} :${availabilityBloc.availableMainWeekInfo?.EditRight}');
                        
                                return GestureDetector(
                                  onTap: availabilityBloc.availableMainWeekInfo
                                              ?.EditRight ==
                                          true
                                      ? () {
                                          print(
                                              "Week $year - $weekNumber was tapped.");

                                          // Handle the onTap action.
                                          AppNavigation.nextScreen(
                                              context,
                                              WeekAvailabilityInfoScreen(
                                                index: weekNumber,
                                                iosWeekYear:
                                                    '$i${weekNumber.toString().padLeft(2, '0')}',
                                                dateRange: dateRange,
                                              ));
                                        }
                                      : null,
                                  child: Container(
                                    color:
                                        context.themeColors.homeContainerColor,
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: AppSize.w12,
                                          ),
                                          child: Row(
                                            children: [
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'Week: $weekNumber',
                                                    style: context
                                                        .textTheme.bodyMedium
                                                        ?.copyWith(
                                                      color: context.themeColors
                                                          .textColor,
                                                      fontSize: AppSize.sp15,
                                                    ),
                                                  ),
                                                  SpaceV(AppSize.h2),
                                                  Text(
                                                    DateFormat('dd-MM',
                                                                appDB.language)
                                                            .format(dateRange
                                                                .start) +
                                                        ' t/m ' +
                                                        DateFormat('dd-MM',
                                                                appDB.language)
                                                            .format(
                                                                dateRange.end),
                                                    style: context
                                                        .textTheme.titleMedium
                                                        ?.copyWith(
                                                      fontSize: AppSize.sp13,
                                                      color: context.themeColors
                                                          .darkGreyColor,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Spacer(),
                                              if (availabilityBloc
                                                      .availableMainWeekInfo
                                                      ?.EditRight ==
                                                  true) ...{
                                                ValueListenableBuilder(
                                                  valueListenable:
                                                      availabilityBloc
                                                          .weekDaysEditedMap,
                                                  builder:
                                                      (context, value, child) {
                                                    if (availabilityBloc
                                                            .weekDaysEditedMap
                                                            .value[weekNumber] ==
                                                        true) {
                                                      return IconButton(
                                                          onPressed: () {},
                                                          icon: Icon(
                                                            Ionicons
                                                                .checkmark_circle_outline,
                                                            color: context
                                                                .themeColors
                                                                .darkGreyColor,
                                                            size: AppSize.sp20,
                                                          ));
                                                    } else {
                                                      return Container();
                                                    }
                                                  },
                                                ),
                                              } else ...{
                                                IconButton(
                                                  onPressed: () {
                                                    // Handle the button click.
                                                  },
                                                  icon: Icon(
                                                    Ionicons.lock_closed,
                                                    color: context.themeColors
                                                        .darkGreyColor,
                                                    size: AppSize.sp20,
                                                  ),
                                                )
                                              },
                                              IconButton(
                                                onPressed: () {
                                                  // Handle the button click.
                                                },
                                                icon: Icon(
                                                  Ionicons
                                                      .chevron_forward_outline,
                                                  color: context
                                                      .themeColors.greyColor,
                                                  size: AppSize.sp18,
                                                ),
                                              )
                                            ],
                                          ),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.only(
                                              left: AppSize.w12),
                                          child: Divider(
                                            thickness: 1,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }
                            },
                            // childCount:
                            childCount: (i == availabilityBloc.currentYear)
                                ? availabilityBloc.remainingWeeks
                                : 50 -
                                    availabilityBloc
                                        .remainingWeeks, // Set the count based on the year
                            // Set the count based on the year
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            }
          ],
        ),
      ),
    );
  }
}
